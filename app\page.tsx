"use client"

import { useState } from "react"
import {
  Building2,
  Users,
  FileText,
  Home,
  CreditCard,
  TrendingUp,
  Search,
  Download,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, ResponsiveContainer } from "recharts"

// Mock data
const maintenanceData = {
  totalDue: 125000,
  totalCollected: 98500,
  pendingAmount: 26500,
  recentPayments: [
    { id: 1, resident: "Rajesh Kumar", flatNo: "A-101", amount: 2500, status: "Paid", date: "2024-01-15" },
    { id: 2, resident: "Priya Sharma", flatNo: "B-205", amount: 2500, status: "Paid", date: "2024-01-14" },
    { id: 3, resident: "Amit Patel", flatNo: "C-301", amount: 2500, status: "Pending", date: "2024-01-10" },
    { id: 4, resident: "Sunita Gupta", flatNo: "A-102", amount: 2500, status: "Paid", date: "2024-01-12" },
    { id: 5, resident: "Vikram Singh", flatNo: "B-103", amount: 2500, status: "Overdue", date: "2024-01-08" },
  ],
}

const residentsData = [
  {
    id: 1,
    name: "Rajesh Kumar",
    flatNo: "A-101",
    block: "A",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    status: "Active",
  },
  {
    id: 2,
    name: "Priya Sharma",
    flatNo: "B-205",
    block: "B",
    phone: "+91 98765 43211",
    email: "<EMAIL>",
    status: "Active",
  },
  {
    id: 3,
    name: "Amit Patel",
    flatNo: "C-301",
    block: "C",
    phone: "+91 98765 43212",
    email: "<EMAIL>",
    status: "Active",
  },
  {
    id: 4,
    name: "Sunita Gupta",
    flatNo: "A-102",
    block: "A",
    phone: "+91 98765 43213",
    email: "<EMAIL>",
    status: "Inactive",
  },
  {
    id: 5,
    name: "Vikram Singh",
    flatNo: "B-103",
    block: "B",
    phone: "+91 98765 43214",
    email: "<EMAIL>",
    status: "Active",
  },
]

const chartData = [
  { name: "Paid", value: 78, color: "#10b981" },
  { name: "Pending", value: 15, color: "#f59e0b" },
  { name: "Overdue", value: 7, color: "#ef4444" },
]

const monthlyData = [
  { month: "Jan", paid: 85, unpaid: 15 },
  { month: "Feb", paid: 78, unpaid: 22 },
  { month: "Mar", paid: 92, unpaid: 8 },
  { month: "Apr", paid: 88, unpaid: 12 },
  { month: "May", paid: 95, unpaid: 5 },
  { month: "Jun", paid: 82, unpaid: 18 },
]

const navigation = [
  { name: "Dashboard", href: "#", icon: Home, current: true },
  { name: "Maintenance", href: "#", icon: CreditCard, current: false },
  { name: "Residents", href: "#", icon: Users, current: false },
  { name: "Reports", href: "#", icon: FileText, current: false },
]

export default function HousingSocietyDashboard() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [searchTerm, setSearchTerm] = useState("")
  const [filterBlock, setFilterBlock] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")

  const filteredResidents = residentsData.filter((resident) => {
    const matchesSearch =
      resident.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resident.flatNo.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesBlock = filterBlock === "all" || resident.block === filterBlock
    const matchesStatus = filterStatus === "all" || resident.status.toLowerCase() === filterStatus.toLowerCase()

    return matchesSearch && matchesBlock && matchesStatus
  })

  const AppSidebar = () => (
    <Sidebar>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Building2 className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-semibold">Housing Society</span>
                <span className="text-xs">Management System</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton
                    asChild
                    isActive={activeTab === item.name.toLowerCase()}
                    onClick={() => setActiveTab(item.name.toLowerCase())}
                  >
                    <button className="w-full">
                      <item.icon className="size-4" />
                      <span>{item.name}</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )

  const MaintenanceDashboard = () => (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Due This Month</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{maintenanceData.totalDue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">From 100 flats</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Collected</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">₹{maintenanceData.totalCollected.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">78% collection rate</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">₹{maintenanceData.pendingAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">22 flats pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">January 2024</div>
            <Button size="sm" className="mt-2 w-full">
              <Plus className="h-4 w-4 mr-2" />
              Generate Bills
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Payments</CardTitle>
          <CardDescription>Latest maintenance payments from residents</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Resident Name</TableHead>
                <TableHead>Flat No.</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {maintenanceData.recentPayments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">{payment.resident}</TableCell>
                  <TableCell>{payment.flatNo}</TableCell>
                  <TableCell>₹{payment.amount.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        payment.status === "Paid"
                          ? "default"
                          : payment.status === "Pending"
                            ? "secondary"
                            : "destructive"
                      }
                    >
                      {payment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{payment.date}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Payment
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )

  const ResidentDirectory = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Resident Directory</CardTitle>
          <CardDescription>Manage and view all society residents</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search residents or flat numbers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={filterBlock} onValueChange={setFilterBlock}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by Block" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Blocks</SelectItem>
                <SelectItem value="A">Block A</SelectItem>
                <SelectItem value="B">Block B</SelectItem>
                <SelectItem value="C">Block C</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Flat No.</TableHead>
                <TableHead>Block</TableHead>
                <TableHead className="hidden md:table-cell">Phone</TableHead>
                <TableHead className="hidden lg:table-cell">Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredResidents.map((resident) => (
                <TableRow key={resident.id}>
                  <TableCell className="font-medium">{resident.name}</TableCell>
                  <TableCell>{resident.flatNo}</TableCell>
                  <TableCell>{resident.block}</TableCell>
                  <TableCell className="hidden md:table-cell">{resident.phone}</TableCell>
                  <TableCell className="hidden lg:table-cell">{resident.email}</TableCell>
                  <TableCell>
                    <Badge variant={resident.status === "Active" ? "default" : "secondary"}>{resident.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Details
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )

  const ReportsSection = () => (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Payment Status Distribution</CardTitle>
            <CardDescription>Current month payment status breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                paid: { label: "Paid", color: "#10b981" },
                pending: { label: "Pending", color: "#f59e0b" },
                overdue: { label: "Overdue", color: "#ef4444" },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Collection Trends</CardTitle>
            <CardDescription>Payment collection trends over the last 6 months</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                paid: { label: "Paid", color: "#10b981" },
                unpaid: { label: "Unpaid", color: "#ef4444" },
              }}
              className="h-[300px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyData}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="paid" fill="#10b981" />
                  <Bar dataKey="unpaid" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Generate Reports</CardTitle>
          <CardDescription>Download detailed maintenance and payment reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-medium">Monthly Maintenance Report</h4>
              <p className="text-sm text-muted-foreground">Complete payment status for the month</p>
              <Button className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Defaulter List</h4>
              <p className="text-sm text-muted-foreground">List of residents with pending payments</p>
              <Button variant="outline" className="w-full bg-transparent">
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Financial Summary</h4>
              <p className="text-sm text-muted-foreground">Overall financial status and trends</p>
              <Button variant="outline" className="w-full bg-transparent">
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case "maintenance":
        return <MaintenanceDashboard />
      case "residents":
        return <ResidentDirectory />
      case "reports":
        return <ReportsSection />
      default:
        return <MaintenanceDashboard />
    }
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            <h1 className="font-semibold">Housing Society Management</h1>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 md:p-6">{renderContent()}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}
